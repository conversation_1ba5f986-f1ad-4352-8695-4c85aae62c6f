import db from '../database/connection.js';
import { v4 as uuidv4 } from 'uuid';

// Firestore collection names
export const COLLECTIONS = {
  SCHOOLS: 'schools',
  USERS: 'users',
  CLASSES: 'classes',
  STUDENTS: 'students',
  PARENTS: 'parents',
  STUDENT_PARENTS: 'student_parents',
  ATTENDANCE: 'attendance',
  SMS_MESSAGES: 'sms_messages',
  AI_CHAT_SESSIONS: 'ai_chat_sessions',
  AI_CHAT_MESSAGES: 'ai_chat_messages'
};

// Base model class for common operations
class BaseModel {
  constructor(collectionName) {
    this.collection = db.collection(collectionName);
  }

  async create(data) {
    const id = uuidv4();
    const timestamp = new Date();
    const docData = {
      id,
      ...data,
      created_at: timestamp,
      updated_at: timestamp
    };
    
    await this.collection.doc(id).set(docData);
    return { id, ...docData };
  }

  async findById(id) {
    const doc = await this.collection.doc(id).get();
    if (!doc.exists) {
      return null;
    }
    return { id: doc.id, ...doc.data() };
  }

  async findAll(options = {}) {
    let query = this.collection;

    // Simplified query to avoid index requirements
    if (options.where) {
      // Only apply single field filters to avoid composite index requirements
      const whereEntries = Object.entries(options.where);
      if (whereEntries.length === 1) {
        const [field, value] = whereEntries[0];
        if (typeof value === 'object' && value.op) {
          query = query.where(field, value.op, value.value);
        } else {
          query = query.where(field, '==', value);
        }
      } else {
        // For multiple conditions, just use the first one to avoid index issues
        const [field, value] = whereEntries[0];
        if (typeof value === 'object' && value.op) {
          query = query.where(field, value.op, value.value);
        } else {
          query = query.where(field, '==', value);
        }
      }
    }

    if (options.limit) {
      query = query.limit(options.limit);
    }

    const snapshot = await query.get();
    let results = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Apply additional filtering in memory if needed
    if (options.where && Object.keys(options.where).length > 1) {
      results = results.filter(doc => {
        return Object.entries(options.where).every(([field, value]) => {
          if (typeof value === 'object' && value.op) {
            switch (value.op) {
              case '==': return doc[field] === value.value;
              case '!=': return doc[field] !== value.value;
              case '>': return doc[field] > value.value;
              case '>=': return doc[field] >= value.value;
              case '<': return doc[field] < value.value;
              case '<=': return doc[field] <= value.value;
              case 'in': return value.value.includes(doc[field]);
              default: return doc[field] === value.value;
            }
          } else {
            return doc[field] === value;
          }
        });
      });
    }

    // Apply sorting in memory
    if (options.orderBy) {
      results.sort((a, b) => {
        const aVal = a[options.orderBy.field];
        const bVal = b[options.orderBy.field];
        const direction = options.orderBy.direction === 'desc' ? -1 : 1;

        if (aVal < bVal) return -1 * direction;
        if (aVal > bVal) return 1 * direction;
        return 0;
      });
    }

    return results;
  }

  async update(id, data) {
    const updateData = {
      ...data,
      updated_at: new Date()
    };
    
    await this.collection.doc(id).update(updateData);
    return this.findById(id);
  }

  async delete(id) {
    await this.collection.doc(id).delete();
    return true;
  }

  async findOne(options = {}) {
    const results = await this.findAll({ ...options, limit: 1 });
    return results.length > 0 ? results[0] : null;
  }
}

// School model
export class School extends BaseModel {
  constructor() {
    super(COLLECTIONS.SCHOOLS);
  }

  async create(data) {
    const schoolData = {
      name: data.name,
      address: data.address || null,
      phone: data.phone || null,
      email: data.email || null,
      principal_name: data.principal_name || null
    };
    return super.create(schoolData);
  }
}

// User model
export class User extends BaseModel {
  constructor() {
    super(COLLECTIONS.USERS);
  }

  async create(data) {
    const userData = {
      school_id: data.school_id,
      username: data.username,
      email: data.email,
      password_hash: data.password_hash,
      first_name: data.first_name,
      last_name: data.last_name,
      role: data.role, // 'admin', 'teacher', 'staff'
      phone: data.phone || null,
      is_active: data.is_active !== undefined ? data.is_active : true,
      last_login: data.last_login || null
    };
    return super.create(userData);
  }

  async findByUsername(username) {
    return this.findOne({ where: { username } });
  }

  async findByEmail(email) {
    return this.findOne({ where: { email } });
  }
}

// Class model
export class Class extends BaseModel {
  constructor() {
    super(COLLECTIONS.CLASSES);
  }

  async create(data) {
    const classData = {
      school_id: data.school_id,
      name: data.name,
      grade_level: data.grade_level,
      section: data.section,
      teacher_id: data.teacher_id || null,
      academic_year: data.academic_year,
      is_active: data.is_active !== undefined ? data.is_active : true
    };
    return super.create(classData);
  }

  async findBySchool(schoolId) {
    const allClasses = await this.findAll({
      where: { school_id: schoolId }
    });

    // Filter and sort in memory
    return allClasses
      .filter(cls => cls.is_active)
      .sort((a, b) => a.grade_level - b.grade_level);
  }
}

// Student model
export class Student extends BaseModel {
  constructor() {
    super(COLLECTIONS.STUDENTS);
  }

  async create(data) {
    const studentData = {
      school_id: data.school_id,
      class_id: data.class_id,
      student_number: data.student_number,
      first_name: data.first_name,
      last_name: data.last_name,
      date_of_birth: data.date_of_birth || null,
      gender: data.gender, // 'male', 'female'
      address: data.address || null,
      parent_phone: data.parent_phone || null,
      parent_email: data.parent_email || null,
      emergency_contact: data.emergency_contact || null,
      medical_info: data.medical_info || null,
      enrollment_date: data.enrollment_date || new Date(),
      is_active: data.is_active !== undefined ? data.is_active : true
    };
    return super.create(studentData);
  }

  async findByClass(classId) {
    const allStudents = await this.findAll({
      where: { class_id: classId }
    });

    // Filter and sort in memory
    return allStudents
      .filter(student => student.is_active)
      .sort((a, b) => a.first_name.localeCompare(b.first_name));
  }

  async findByStudentNumber(studentNumber) {
    return this.findOne({ where: { student_number: studentNumber } });
  }
}

// Parent model
export class Parent extends BaseModel {
  constructor() {
    super(COLLECTIONS.PARENTS);
  }

  async create(data) {
    const parentData = {
      student_id: data.student_id,
      first_name: data.first_name,
      last_name: data.last_name,
      relationship: data.relationship, // 'mother', 'father', 'guardian'
      phone: data.phone,
      email: data.email || null,
      address: data.address || null,
      occupation: data.occupation || null,
      is_primary_contact: data.is_primary_contact || false,
      is_active: data.is_active !== undefined ? data.is_active : true
    };
    return super.create(parentData);
  }

  async findByStudent(studentId) {
    return this.findAll({ 
      where: { student_id: studentId, is_active: true },
      orderBy: { field: 'is_primary_contact', direction: 'desc' }
    });
  }
}

// SMS Message model
export class SmsMessage extends BaseModel {
  constructor() {
    super(COLLECTIONS.SMS_MESSAGES);
  }

  async create(data) {
    const smsData = {
      school_id: data.school_id,
      sender_id: data.sender_id,
      recipient_type: data.recipient_type, // 'individual', 'class', 'grade', 'all'
      recipient_id: data.recipient_id || null,
      message_text: data.message_text,
      original_text: data.original_text || null,
      is_ai_enhanced: data.is_ai_enhanced || false,
      status: data.status || 'pending', // 'pending', 'sent', 'failed', 'cancelled'
      sent_at: data.sent_at || null,
      delivery_count: data.delivery_count || 0,
      failed_count: data.failed_count || 0
    };
    return super.create(smsData);
  }
}

// AI Chat Session model
export class AiChatSession extends BaseModel {
  constructor() {
    super(COLLECTIONS.AI_CHAT_SESSIONS);
  }

  async create(data) {
    const sessionData = {
      user_id: data.user_id,
      session_title: data.session_title || null,
      is_active: data.is_active !== undefined ? data.is_active : true
    };
    return super.create(sessionData);
  }
}

// AI Chat Message model
export class AiChatMessage extends BaseModel {
  constructor() {
    super(COLLECTIONS.AI_CHAT_MESSAGES);
  }

  async create(data) {
    const messageData = {
      session_id: data.session_id,
      role: data.role, // 'user', 'assistant', 'system'
      content: data.content,
      function_calls: data.function_calls || null
    };
    return super.create(messageData);
  }
}

// Create instances for export
export const schoolModel = new School();
export const userModel = new User();
export const classModel = new Class();
export const studentModel = new Student();
export const parentModel = new Parent();
export const smsMessageModel = new SmsMessage();
export const aiChatSessionModel = new AiChatSession();
export const aiChatMessageModel = new AiChatMessage();
