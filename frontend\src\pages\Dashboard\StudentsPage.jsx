import React, { useState, useEffect } from 'react';
import { Search, Filter, Eye, MessageSquare, Users, Plus, Upload, BookOpen } from 'lucide-react';
import { studentsAPI, classesAPI } from '../../services/api';
import AddStudentModal from '../../components/Students/AddStudentModal';
import ImportCSVModal from '../../components/Students/ImportCSVModal';
import AddClassModal from '../../components/Classes/AddClassModal';
import toast from 'react-hot-toast';

const StudentsPage = () => {
  const [students, setStudents] = useState([]);
  const [classes, setClasses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedGrade, setSelectedGrade] = useState('');
  const [pagination, setPagination] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showAddClassModal, setShowAddClassModal] = useState(false);

  // Fetch classes for filter dropdown
  useEffect(() => {
    const fetchClasses = async () => {
      try {
        const response = await classesAPI.getAll();
        if (response.success) {
          setClasses(response.data.classes);
        }
      } catch (error) {
        console.error('Error fetching classes:', error);
      }
    };
    fetchClasses();
  }, []);

  // Fetch students
  const fetchStudents = async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: 20
      };

      if (searchQuery.trim()) {
        params.q = searchQuery.trim();
      }
      if (selectedClass) {
        params.class_id = selectedClass;
      }
      if (selectedGrade) {
        params.grade_level = selectedGrade;
      }

      const response = await studentsAPI.getAll(params);
      if (response.success) {
        setStudents(response.data.students);
        setPagination(response.data.pagination);
        setCurrentPage(page);
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('Öğrenci listesi yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchStudents(1);
  }, [searchQuery, selectedClass, selectedGrade]);

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery !== '') {
        fetchStudents(1);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const handlePageChange = (page) => {
    fetchStudents(page);
  };

  const handleStudentAdded = () => {
    fetchStudents(currentPage);
  };

  const handleImportComplete = () => {
    fetchStudents(1);
    setCurrentPage(1);
  };

  const handleClassAdded = (newClass) => {
    setClasses(prev => [...prev, newClass]);
    toast.success('Sınıf başarıyla eklendi!');
  };

  const getGradeOptions = () => {
    const grades = [...new Set(classes.map(cls => cls.grade_level))].sort();
    return grades;
  };

  const getClassesForGrade = () => {
    if (!selectedGrade) return classes;
    return classes.filter(cls => cls.grade_level === parseInt(selectedGrade));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Öğrenci Yönetimi</h1>
          <p className="text-gray-600">Öğrenci listesi ve detayları</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Users className="h-4 w-4" />
            <span>Toplam: {pagination.total_count || 0} öğrenci</span>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setShowAddClassModal(true)}
              className="flex items-center px-3 py-2 text-green-600 border border-green-600 rounded-md hover:bg-green-50 transition-colors"
            >
              <BookOpen className="h-4 w-4 mr-2" />
              Sınıf Ekle
            </button>
            <button
              onClick={() => setShowImportModal(true)}
              className="flex items-center px-3 py-2 text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 transition-colors"
            >
              <Upload className="h-4 w-4 mr-2" />
              CSV İçe Aktar
            </button>
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              Öğrenci Ekle
            </button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Öğrenci ara (isim, soyisim, numara)"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Grade Filter */}
          <select
            value={selectedGrade}
            onChange={(e) => {
              setSelectedGrade(e.target.value);
              setSelectedClass(''); // Reset class when grade changes
            }}
            className="border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Tüm Sınıflar</option>
            {getGradeOptions().map(grade => (
              <option key={grade} value={grade}>{grade}. Sınıf</option>
            ))}
          </select>

          {/* Class Filter */}
          <select
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={!selectedGrade}
          >
            <option value="">Tüm Şubeler</option>
            {getClassesForGrade().map(cls => (
              <option key={cls.id} value={cls.id}>{cls.name}</option>
            ))}
          </select>

          {/* Clear Filters */}
          <button
            onClick={() => {
              setSearchQuery('');
              setSelectedClass('');
              setSelectedGrade('');
            }}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Filtreleri Temizle
          </button>
        </div>
      </div>

      {/* Students List */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Öğrenciler yükleniyor...</p>
          </div>
        ) : students.length === 0 ? (
          <div className="p-8 text-center">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Öğrenci bulunamadı</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Öğrenci
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Sınıf
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Veli İletişim
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      İşlemler
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {students.map((student) => (
                    <StudentRow key={student.id} student={student} />
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination.total_pages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={!pagination.has_prev}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    Önceki
                  </button>
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={!pagination.has_next}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    Sonraki
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      <span className="font-medium">{((currentPage - 1) * pagination.per_page) + 1}</span>
                      {' - '}
                      <span className="font-medium">
                        {Math.min(currentPage * pagination.per_page, pagination.total_count)}
                      </span>
                      {' / '}
                      <span className="font-medium">{pagination.total_count}</span>
                      {' sonuç gösteriliyor'}
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={!pagination.has_prev}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                      >
                        Önceki
                      </button>
                      <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                        {currentPage} / {pagination.total_pages}
                      </span>
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={!pagination.has_next}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                      >
                        Sonraki
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Modals */}
      <AddClassModal
        isOpen={showAddClassModal}
        onClose={() => setShowAddClassModal(false)}
        onSuccess={handleClassAdded}
      />

      <AddStudentModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onStudentAdded={handleStudentAdded}
      />

      <ImportCSVModal
        isOpen={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImportComplete={handleImportComplete}
      />
    </div>
  );
};

const StudentRow = ({ student }) => {
  const primaryParent = student.Parents?.find(p => p.is_primary_contact) || student.Parents?.[0];

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        <div>
          <div className="text-sm font-medium text-gray-900">
            {student.first_name} {student.last_name}
          </div>
          <div className="text-sm text-gray-500">No: {student.student_number}</div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{student.Class?.name}</div>
        <div className="text-sm text-gray-500">{student.Class?.grade_level}. Sınıf</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        {primaryParent ? (
          <div>
            <div className="text-sm font-medium text-gray-900">
              {primaryParent.first_name} {primaryParent.last_name}
            </div>
            <div className="text-sm text-gray-500">{primaryParent.phone}</div>
            <div className="text-xs text-gray-400">{primaryParent.relationship}</div>
          </div>
        ) : (
          <span className="text-sm text-gray-400">Veli bilgisi yok</span>
        )}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
        <button className="text-blue-600 hover:text-blue-900 inline-flex items-center">
          <Eye className="h-4 w-4 mr-1" />
          Detay
        </button>
        <button className="text-green-600 hover:text-green-900 inline-flex items-center">
          <MessageSquare className="h-4 w-4 mr-1" />
          SMS
        </button>
      </td>
    </tr>
  );
};

export default StudentsPage;
