import { aiChatSessionModel, aiChatMessageModel, userModel } from '../models/index.js';
import { processGeminiMessage } from '../services/geminiService.js';
import Jo<PERSON> from 'joi';

// Validation schemas
const chatMessageSchema = Joi.object({
  message: Joi.string().min(1).max(2000).required(),
  session_id: Joi.string().optional()
});

const createSessionSchema = Joi.object({
  title: Joi.string().min(1).max(255).optional()
});

// Create new chat session
export const createChatSession = async (req, res) => {
  try {
    const { error, value } = createSessionSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { title } = value;
    const userId = req.user.id;

    const session = await aiChatSessionModel.create({
      user_id: userId,
      session_title: title || 'Ye<PERSON>',
      is_active: true
    });

    res.json({
      success: true,
      data: session,
      message: 'Chat oturumu başarıyla oluşturuldu'
    });

  } catch (error) {
    console.error('Create chat session error:', error);
    res.status(500).json({
      success: false,
      message: 'Chat oturumu oluşturulurken hata oluştu',
      error: error.message
    });
  }
};

// Send message to AI
export const sendMessage = async (req, res) => {
  try {
    const { error, value } = chatMessageSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { message, session_id } = value;
    const userId = req.user.id;
    const user = req.user;

    let sessionId = session_id;

    // Create new session if not provided
    if (!sessionId) {
      const session = await aiChatSessionModel.create({
        user_id: userId,
        session_title: message.substring(0, 50) + '...',
        is_active: true
      });
      sessionId = session.id;
    }

    // Verify session belongs to user
    const session = await aiChatSessionModel.findById(sessionId);
    if (!session || session.user_id !== userId) {
      return res.status(404).json({
        success: false,
        message: 'Chat oturumu bulunamadı'
      });
    }

    // Save user message
    await aiChatMessageModel.create({
      session_id: sessionId,
      role: 'user',
      content: message
    });

    // Get user context for AI
    const userContext = {
      userId: user.id,
      userName: `${user.first_name} ${user.last_name}`,
      role: user.role,
      schoolId: user.school_id,
      schoolName: 'Nakipoğlu Cumhuriyet Anadolu Lisesi' // This should come from school data
    };

    // Process message with Gemini
    const aiResponse = await processGeminiMessage(message, userContext);

    if (!aiResponse.success) {
      return res.status(500).json({
        success: false,
        message: 'AI yanıtı alınırken hata oluştu',
        error: aiResponse.error
      });
    }

    // Save AI response
    const aiMessage = await aiChatMessageModel.create({
      session_id: sessionId,
      role: 'assistant',
      content: aiResponse.response,
      function_calls: aiResponse.functionCall ? JSON.stringify(aiResponse.functionCall) : null
    });

    res.json({
      success: true,
      data: {
        session_id: sessionId,
        user_message: {
          role: 'user',
          content: message,
          timestamp: new Date()
        },
        ai_response: {
          role: 'assistant',
          content: aiResponse.response,
          timestamp: aiMessage.created_at,
          function_call: aiResponse.functionCall
        }
      },
      message: 'Mesaj başarıyla gönderildi'
    });

  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({
      success: false,
      message: 'Mesaj gönderilirken hata oluştu',
      error: error.message
    });
  }
};

// Get chat sessions
export const getChatSessions = async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 20, is_active } = req.query;

    let whereConditions = {
      user_id: userId
    };

    if (is_active !== undefined) {
      whereConditions.is_active = is_active === 'true';
    }

    const sessions = await aiChatSessionModel.findAll({
      where: whereConditions,
      limit: parseInt(limit),
      orderBy: { field: 'updated_at', direction: 'desc' }
    });

    res.json({
      success: true,
      data: sessions
    });

  } catch (error) {
    console.error('Get chat sessions error:', error);
    res.status(500).json({
      success: false,
      message: 'Chat oturumları alınırken hata oluştu',
      error: error.message
    });
  }
};

// Get chat messages
export const getChatMessages = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user.id;
    const { limit = 50 } = req.query;

    // Verify session belongs to user
    const session = await aiChatSessionModel.findById(sessionId);
    if (!session || session.user_id !== userId) {
      return res.status(404).json({
        success: false,
        message: 'Chat oturumu bulunamadı'
      });
    }

    const messages = await aiChatMessageModel.findAll({
      where: { session_id: sessionId },
      limit: parseInt(limit),
      orderBy: { field: 'created_at', direction: 'asc' }
    });

    res.json({
      success: true,
      data: {
        session,
        messages: messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          function_calls: msg.function_calls ? JSON.parse(msg.function_calls) : null,
          timestamp: msg.created_at
        }))
      }
    });

  } catch (error) {
    console.error('Get chat messages error:', error);
    res.status(500).json({
      success: false,
      message: 'Chat mesajları alınırken hata oluştu',
      error: error.message
    });
  }
};

// Delete chat session
export const deleteChatSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user.id;

    // Verify session belongs to user
    const session = await aiChatSessionModel.findById(sessionId);
    if (!session || session.user_id !== userId) {
      return res.status(404).json({
        success: false,
        message: 'Chat oturumu bulunamadı'
      });
    }

    // Delete all messages in the session
    const messages = await aiChatMessageModel.findAll({
      where: { session_id: sessionId }
    });

    for (const message of messages) {
      await aiChatMessageModel.delete(message.id);
    }

    // Delete the session
    await aiChatSessionModel.delete(sessionId);

    res.json({
      success: true,
      message: 'Chat oturumu başarıyla silindi'
    });

  } catch (error) {
    console.error('Delete chat session error:', error);
    res.status(500).json({
      success: false,
      message: 'Chat oturumu silinirken hata oluştu',
      error: error.message
    });
  }
};

// Update session title
export const updateSessionTitle = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { title } = req.body;
    const userId = req.user.id;

    if (!title || title.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Başlık boş olamaz'
      });
    }

    // Verify session belongs to user
    const session = await aiChatSessionModel.findById(sessionId);
    if (!session || session.user_id !== userId) {
      return res.status(404).json({
        success: false,
        message: 'Chat oturumu bulunamadı'
      });
    }

    const updatedSession = await aiChatSessionModel.update(sessionId, {
      session_title: title.trim()
    });

    res.json({
      success: true,
      data: updatedSession,
      message: 'Oturum başlığı başarıyla güncellendi'
    });

  } catch (error) {
    console.error('Update session title error:', error);
    res.status(500).json({
      success: false,
      message: 'Oturum başlığı güncellenirken hata oluştu',
      error: error.message
    });
  }
};
