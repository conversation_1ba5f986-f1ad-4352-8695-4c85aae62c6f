# Test Environment Variables
NODE_ENV=test
PORT=3001

# Firebase Test Configuration
FIREBASE_PROJECT_ID=test-project-id
FIREBASE_PRIVATE_KEY_ID=test-private-key-id
FIREBASE_PRIVATE_KEY="-----B<PERSON><PERSON> PRIVATE KEY-----\ntest-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=test-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/test%40test-project.iam.gserviceaccount.com

# JWT (Test)
JWT_SECRET=test-jwt-secret-key-for-testing-only
JWT_EXPIRES_IN=1h
BCRYPT_ROUNDS=4

# Gemini AI (Mock in tests)
GEMINI_API_KEY=test-gemini-key

# Netgsm (Mock in tests)
NETGSM_USERNAME=test-user
NETGSM_PASSWORD=test-pass
NETGSM_HEADER=TEST
