import { smsMessageModel, studentModel, classModel, parentModel, schoolModel, userModel } from '../models/index.js';
import { enhanceTextWithGemini } from '../services/geminiService.js';
import smsService from '../services/smsService.js';
import Jo<PERSON> from 'joi';
import dotenv from 'dotenv';

dotenv.config();

// Validation schemas
const enhanceTextSchema = Joi.object({
  text: Joi.string().min(1).max(1000).required(),
  tone: Joi.string().valid('formal', 'friendly', 'urgent', 'informative').default('formal'),
  context: Joi.string().max(200).optional()
});

const sendSmsSchema = Joi.object({
  recipient_type: Joi.string().valid('individual', 'class', 'grade').required(),
  recipient_id: Joi.string().required(),
  message: Joi.string().min(1).max(1000).required(),
  original_text: Joi.string().max(1000).optional(),
  is_ai_enhanced: Joi.boolean().default(false)
});

// Enhance text with AI (<PERSON><PERSON><PERSON>)
export const enhanceText = async (req, res) => {
  try {
    const { error, value } = enhanceTextSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { text, tone, context } = value;

    // Use Gemini to enhance text
    const result = await enhanceTextWithGemini(text, tone, context);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Metin iyileştirme sırasında hata oluştu',
        error: result.error
      });
    }

    res.json({
      success: true,
      data: {
        original_text: text,
        enhanced_text: result.enhancedText,
        tone: tone,
        character_count: result.enhancedText.length
      },
      message: 'Metin başarıyla iyileştirildi'
    });

  } catch (error) {
    console.error('Text enhancement error:', error);
    res.status(500).json({
      success: false,
      message: 'Metin iyileştirme sırasında hata oluştu',
      error: error.message
    });
  }
};

// Send SMS
export const sendSms = async (req, res) => {
  try {
    const { error, value } = sendSmsSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { recipient_type, recipient_id, message, original_text, is_ai_enhanced } = value;
    const userId = req.user.id;
    const schoolId = req.user.school_id;

    // Create SMS message record
    const smsMessage = await smsMessageModel.create({
      school_id: schoolId,
      sender_id: userId,
      recipient_type,
      recipient_id,
      message_text: message,
      original_text,
      is_ai_enhanced,
      status: 'pending'
    });

    // Get recipient information based on type
    let recipientInfo;
    let smsResult;

    if (recipient_type === 'individual') {
      // Get student and parent info
      const student = await studentModel.findById(recipient_id);
      if (!student || student.school_id !== schoolId) {
        return res.status(404).json({
          success: false,
          message: 'Öğrenci bulunamadı'
        });
      }

      const parents = await parentModel.findByStudent(student.id);
      const primaryParent = parents.find(p => p.is_primary_contact) || parents[0];
      
      if (!primaryParent) {
        return res.status(404).json({
          success: false,
          message: 'Öğrenci için veli bilgisi bulunamadı'
        });
      }

      recipientInfo = {
        parent_phone: primaryParent.phone,
        student_name: `${student.first_name} ${student.last_name}`
      };

      // Send SMS
      smsResult = await smsService.sendSingle(recipientInfo.parent_phone, message);

      if (smsResult.success) {
        await smsMessageModel.update(smsMessage.id, {
          status: 'sent',
          sent_at: new Date(),
          delivery_count: 1
        });
      } else {
        await smsMessageModel.update(smsMessage.id, {
          status: 'failed',
          failed_count: 1
        });

        return res.status(500).json({
          success: false,
          message: `SMS gönderilemedi: ${smsResult.error}`
        });
      }

    } else if (recipient_type === 'class') {
      // Get class and students
      const classInfo = await classModel.findById(recipient_id);
      if (!classInfo || classInfo.school_id !== schoolId) {
        return res.status(404).json({
          success: false,
          message: 'Sınıf bulunamadı'
        });
      }

      const students = await studentModel.findByClass(recipient_id);
      const phoneNumbers = [];

      for (const student of students) {
        const parents = await parentModel.findByStudent(student.id);
        const primaryParent = parents.find(p => p.is_primary_contact) || parents[0];
        if (primaryParent && primaryParent.phone) {
          phoneNumbers.push(primaryParent.phone);
        }
      }

      if (phoneNumbers.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Sınıf için veli telefon numarası bulunamadı'
        });
      }

      // Send bulk SMS
      smsResult = await smsService.sendBulk(phoneNumbers, message);

      await smsMessageModel.update(smsMessage.id, {
        status: smsResult.successful > 0 ? 'sent' : 'failed',
        sent_at: new Date(),
        delivery_count: smsResult.successful,
        failed_count: smsResult.failed
      });

      recipientInfo = {
        class_name: classInfo.name,
        student_count: students.length,
        parent_count: phoneNumbers.length
      };
    }

    res.json({
      success: true,
      data: {
        messageId: smsMessage.id,
        recipientType: recipient_type,
        recipientInfo,
        deliveryStatus: {
          successful: smsResult.successful || (smsResult.success ? 1 : 0),
          failed: smsResult.failed || (smsResult.success ? 0 : 1),
          total: smsResult.totalSent || 1
        },
        message: message,
        isAiEnhanced: is_ai_enhanced
      },
      message: 'SMS başarıyla gönderildi'
    });

  } catch (error) {
    console.error('SMS sending error:', error);
    res.status(500).json({
      success: false,
      message: 'SMS gönderimi sırasında hata oluştu',
      error: error.message
    });
  }
};

// Get SMS history
export const getSmsHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const schoolId = req.user.school_id;
    const { page = 1, limit = 20, status, recipient_type } = req.query;

    let whereConditions = {
      school_id: schoolId
    };

    if (status) {
      whereConditions.status = status;
    }

    if (recipient_type) {
      whereConditions.recipient_type = recipient_type;
    }

    const messages = await smsMessageModel.findAll({
      where: whereConditions,
      limit: parseInt(limit),
      orderBy: { field: 'created_at', direction: 'desc' }
    });

    // Get sender information for each message
    const messagesWithSender = await Promise.all(
      messages.map(async (msg) => {
        const sender = await userModel.findById(msg.sender_id);
        return {
          ...msg,
          sender: sender ? {
            name: `${sender.first_name} ${sender.last_name}`,
            role: sender.role
          } : null
        };
      })
    );

    res.json({
      success: true,
      data: {
        messages: messagesWithSender,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: messages.length
        }
      }
    });

  } catch (error) {
    console.error('SMS history error:', error);
    res.status(500).json({
      success: false,
      message: 'SMS geçmişi alınırken hata oluştu',
      error: error.message
    });
  }
};

// Get SMS statistics
export const getSmsStats = async (req, res) => {
  try {
    const schoolId = req.user.school_id;
    const { period = '30' } = req.query; // days

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    const messages = await smsMessageModel.findAll({
      where: {
        school_id: schoolId,
        created_at: { op: '>=', value: startDate }
      }
    });

    const stats = {
      total: messages.length,
      sent: messages.filter(m => m.status === 'sent').length,
      failed: messages.filter(m => m.status === 'failed').length,
      pending: messages.filter(m => m.status === 'pending').length,
      ai_enhanced: messages.filter(m => m.is_ai_enhanced).length,
      by_type: {
        individual: messages.filter(m => m.recipient_type === 'individual').length,
        class: messages.filter(m => m.recipient_type === 'class').length,
        grade: messages.filter(m => m.recipient_type === 'grade').length,
        all: messages.filter(m => m.recipient_type === 'all').length
      }
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('SMS stats error:', error);
    res.status(500).json({
      success: false,
      message: 'SMS istatistikleri alınırken hata oluştu',
      error: error.message
    });
  }
};
