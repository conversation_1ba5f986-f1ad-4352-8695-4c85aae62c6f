{"name": "smart-school-management", "version": "1.0.0", "description": "AI-Powered Smart School Management and Parent Communication Platform", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test"}, "keywords": ["school-management", "ai-assistant", "sms-platform", "parent-communication", "education-technology"], "author": "Smart School Management Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"@google/generative-ai": "^0.24.1", "firebase-admin": "^13.4.0", "uuid": "^11.1.0"}}