# 🎓 Lucy for School - Yapay Zeka Destekli Okul Yönetim ve Veli İletişim Platformu

Modern web teknolojileri ve yapay zeka ile geliştirilmiş, okul yönetiminin velilerle olan iletişimini kolaylaştıran kapsamlı bir platform.

## 🌟 Özellikler

### 🔐 Güvenli Yönetici Paneli
- JWT tabanlı kimlik doğrulama sistemi
- Rol bazlı yetkilendirme (Admin, Öğretmen, Müdür)
- Güvenli session yönetimi

### 👥 Öğrenci ve Sınıf Yönetimi
- Kapsamlı öğrenci listesi ve detay görüntüleme
- Sınıf bazlı filtreleme ve arama
- Veli iletişim bilgileri yönetimi
- Gerçek zamanlı veri senkronizasyonu

### 📱 Akıllı SMS Sistemi
- Bireysel ve toplu SMS gönderimi
- Netgsm API entegrasyonu
- SMS geçmişi ve istatistikleri
- G<PERSON><PERSON><PERSON> durumu takibi

### ✨ <PERSON><PERSON><PERSON> (AI Metin İyileştirme)
- Kısa notları profesyonel SMS'lere dönüştürme
- Farklı ton seçenekleri (Resmi, Samimi, Acil, Bilgilendirici)
- OpenAI GPT-4 entegrasyonu
- Gerçek zamanlı metin iyileştirme

### 🤖 AI Agent (Yapay Zeka Asistanı)
- Doğal dil komutları ile sistem kontrolü
- Function calling ile backend entegrasyonu
- Sohbet geçmişi ve session yönetimi
- Akıllı komut anlama ve işleme

### 📊 Dashboard ve Raporlama
- Gerçek zamanlı istatistikler
- Aktivite takibi
- Sistem durumu monitörü
- Kullanıcı dostu arayüz

## 🛠️ Teknoloji Stack

### Frontend
- **React 19** - Modern UI framework
- **Vite** - Hızlı geliştirme ortamı
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Zustand** - State management
- **React Hook Form** - Form yönetimi
- **Axios** - HTTP client
- **Lucide React** - Icon library

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **Firebase Firestore** - NoSQL veritabanı
- **Firebase Admin SDK** - Backend Firebase entegrasyonu
- **JWT** - Authentication
- **bcryptjs** - Password hashing
- **Google Gemini AI** - AI integration
- **Netgsm API** - SMS service

### DevOps & Tools
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Jest** - Testing framework
- **Concurrently** - Parallel script execution

## 🚀 Kurulum

### Gereksinimler
- Node.js 18+
- Firebase projesi ve Service Account Key
- Google Gemini API Key
- Netgsm API credentials

### 1. Projeyi Klonlayın
```bash
git clone <repository-url>
cd smart-school-management
```

### 2. Bağımlılıkları Yükleyin
```bash
npm run install:all
```

### 3. Firebase Projesini Kurun
1. [Firebase Console](https://console.firebase.google.com)'da yeni bir proje oluşturun
2. Firestore Database'i etkinleştirin
3. Service Account Key oluşturun:
   - Project Settings > Service Accounts
   - "Generate new private key" butonuna tıklayın
   - JSON dosyasını indirin

### 4. Environment Dosyalarını Yapılandırın

#### Backend (.env)
```bash
cp backend/.env.example backend/.env
```

Aşağıdaki değerleri düzenleyin:
```env
# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key-here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your-service-account-email%40your-project.iam.gserviceaccount.com

# JWT
JWT_SECRET=your-super-secret-jwt-key-min-32-chars

# Google Gemini AI
GEMINI_API_KEY=your-gemini-api-key

# Netgsm SMS
NETGSM_USERNAME=your-netgsm-username
NETGSM_PASSWORD=your-netgsm-password
NETGSM_HEADER=LUCYSCHOOL
```

#### Frontend (.env)
```bash
# Frontend klasöründe
echo "VITE_API_BASE_URL=http://localhost:3000/api" > frontend/.env
```

### 5. Örnek Verileri Oluşturun
```bash
# Backend klasöründe
cd backend
node create-firebase-data.js
```

### 6. Uygulamayı Başlatın
```bash
# Ana dizinde - Hem frontend hem backend'i aynı anda başlatır
npm run dev

# Veya ayrı ayrı:
npm run dev:backend  # Port 3000
npm run dev:frontend # Port 5173
```

## 📖 Kullanım Kılavuzu

### İlk Kurulum
1. Uygulamayı başlattıktan sonra `http://localhost:5173` adresine gidin
2. Varsayılan admin hesabı ile giriş yapın:
   - **Kullanıcı adı:** admin
   - **Şifre:** admin123
3. Okul bilgilerini ve kullanıcı ayarlarını yapılandırın

### Öğrenci Yönetimi
1. **Dashboard > Öğrenciler** menüsüne gidin
2. Öğrenci listesini görüntüleyin ve filtreleyin
3. Öğrenci detaylarını görüntülemek için "Detay" butonuna tıklayın
4. Veli iletişim bilgilerini kontrol edin

### SMS Gönderimi
1. **Dashboard > SMS Gönder** menüsüne gidin
2. Alıcı türünü seçin (Bireysel/Sınıf)
3. Mesajınızı yazın
4. "Sihirli Kalem" ile mesajı iyileştirin (opsiyonel)
5. SMS'i gönderin

### AI Asistan Kullanımı (Gemini)
1. **Dashboard > AI Asistan** menüsüne gidin
2. Doğal dil komutları yazın:
   - "9-A sınıfındaki öğrencileri listele"
   - "Ali Yılmaz'ın veli bilgilerini göster"
   - "10-B sınıfına toplantı mesajı gönder"
3. Gemini AI asistanın yanıtlarını takip edin

## 🧪 Test

### Backend Testleri
```bash
cd backend
npm test
```

### Frontend Testleri
```bash
cd frontend
npm test
```

### Tüm Testler
```bash
npm test
```

## 📁 Proje Yapısı

```
smart-school-management/
├── backend/
│   ├── src/
│   │   ├── controllers/     # API controllers
│   │   ├── middleware/      # Express middleware
│   │   ├── models/          # Sequelize models
│   │   ├── routes/          # API routes
│   │   ├── services/        # Business logic
│   │   ├── database/        # DB config & migrations
│   │   ├── config/          # App configuration
│   │   └── utils/           # Utility functions
│   ├── tests/               # Backend tests
│   └── package.json
├── frontend/
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── pages/           # Page components
│   │   ├── services/        # API services
│   │   ├── store/           # State management
│   │   ├── hooks/           # Custom hooks
│   │   ├── utils/           # Utility functions
│   │   └── types/           # TypeScript types
│   ├── public/              # Static assets
│   └── package.json
└── README.md
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - Kullanıcı girişi
- `POST /api/auth/logout` - Çıkış
- `GET /api/auth/profile` - Profil bilgileri

### Students
- `GET /api/students` - Öğrenci listesi
- `GET /api/students/:id` - Öğrenci detayı
- `GET /api/students/search` - Öğrenci arama

### Classes
- `GET /api/classes` - Sınıf listesi
- `GET /api/classes/:id` - Sınıf detayı
- `GET /api/classes/:id/students` - Sınıf öğrencileri

### SMS
- `POST /api/sms/send` - SMS gönder
- `POST /api/sms/enhance` - Metin iyileştir
- `GET /api/sms/history` - SMS geçmişi

### AI
- `POST /api/ai/chat` - AI ile sohbet
- `GET /api/ai/sessions` - Sohbet oturumları

## 🔒 Güvenlik

- JWT tabanlı kimlik doğrulama
- Bcrypt ile şifre hashleme
- Rate limiting
- CORS koruması
- Helmet.js güvenlik başlıkları
- Input validation (Joi)
- SQL injection koruması (Sequelize ORM)

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Environment Variables (Production)
```env
NODE_ENV=production
JWT_SECRET=your-production-jwt-secret
DB_HOST=your-production-db-host
OPENAI_API_KEY=your-openai-api-key
```

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 📞 Destek

Herhangi bir sorun veya öneriniz için:
- Issue oluşturun
- E-posta gönderin: <EMAIL>

## 🙏 Teşekkürler

- OpenAI - GPT-4 API
- Netgsm - SMS servisi
- Tüm açık kaynak katkıda bulunanlar

---

**Lucy for School Platform** - Eğitimde teknoloji ve AI'ın gücü 🎓✨
