{"name": "smart-school-backend", "version": "1.0.0", "description": "Backend API for Smart School Management Platform", "main": "src/index.js", "type": "module", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo 'Build completed'", "test": "jest", "test:watch": "jest --watch", "firebase:seed": "node create-firebase-data.js"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "openai": "^4.20.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2", "sqlite3": "^5.1.7", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["express", "api", "school-management", "ai-integration"], "author": "Smart School Management Team", "license": "MIT"}