import express from 'express';
import {
  getClasses,
  getClassById,
  createClass
} from '../controllers/classesController.js';
import { authenticate } from '../middleware/auth.js';

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Class routes
router.get('/', getClasses);
router.post('/', createClass);
router.get('/:id', getClassById);

export default router;
