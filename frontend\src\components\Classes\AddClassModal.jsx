import React, { useState, useEffect } from 'react';
import { X, Save, User } from 'lucide-react';
import { classesAPI, usersAPI } from '../../services/api';
import toast from 'react-hot-toast';

const AddClassModal = ({ isOpen, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: '',
    grade_level: '',
    section: '',
    teacher_id: '',
    academic_year: '2024-2025'
  });
  const [teachers, setTeachers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Fetch teachers when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchTeachers();
    }
  }, [isOpen]);

  const fetchTeachers = async () => {
    try {
      const response = await usersAPI.getAll({ role: 'teacher' });
      if (response.success) {
        setTeachers(response.data.users || []);
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
      setTeachers([]);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Sınıf adı gereklidir';
    }

    if (!formData.grade_level) {
      newErrors.grade_level = 'Sınıf seviyesi gereklidir';
    } else if (formData.grade_level < 1 || formData.grade_level > 12) {
      newErrors.grade_level = 'Sınıf seviyesi 1-12 arasında olmalıdır';
    }

    if (!formData.section.trim()) {
      newErrors.section = 'Şube gereklidir';
    }

    if (!formData.academic_year.trim()) {
      newErrors.academic_year = 'Akademik yıl gereklidir';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const response = await classesAPI.create(formData);
      
      if (response.success) {
        toast.success('Sınıf başarıyla oluşturuldu!');
        onSuccess(response.data);
        handleClose();
      } else {
        toast.error(response.message || 'Sınıf oluşturulurken hata oluştu');
      }
    } catch (error) {
      console.error('Error creating class:', error);
      toast.error(error.response?.data?.message || 'Sınıf oluşturulurken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      grade_level: '',
      section: '',
      teacher_id: '',
      academic_year: '2024-2025'
    });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Yeni Sınıf Ekle</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Class Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sınıf Adı *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Örn: 9A, 10B"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1">{errors.name}</p>
            )}
          </div>

          {/* Grade Level and Section */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sınıf Seviyesi *
              </label>
              <select
                name="grade_level"
                value={formData.grade_level}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.grade_level ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Seçiniz</option>
                {[...Array(12)].map((_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {i + 1}. Sınıf
                  </option>
                ))}
              </select>
              {errors.grade_level && (
                <p className="text-red-500 text-sm mt-1">{errors.grade_level}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Şube *
              </label>
              <input
                type="text"
                name="section"
                value={formData.section}
                onChange={handleInputChange}
                placeholder="A, B, C..."
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.section ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.section && (
                <p className="text-red-500 text-sm mt-1">{errors.section}</p>
              )}
            </div>
          </div>

          {/* Teacher */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sınıf Öğretmeni
            </label>
            <select
              name="teacher_id"
              value={formData.teacher_id}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Öğretmen seçiniz (opsiyonel)</option>
              {teachers.map(teacher => (
                <option key={teacher.id} value={teacher.id}>
                  {teacher.first_name} {teacher.last_name}
                </option>
              ))}
            </select>
          </div>

          {/* Academic Year */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Akademik Yıl *
            </label>
            <input
              type="text"
              name="academic_year"
              value={formData.academic_year}
              onChange={handleInputChange}
              placeholder="2024-2025"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.academic_year ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.academic_year && (
              <p className="text-red-500 text-sm mt-1">{errors.academic_year}</p>
            )}
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              İptal
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{loading ? 'Oluşturuluyor...' : 'Sınıf Oluştur'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddClassModal;
