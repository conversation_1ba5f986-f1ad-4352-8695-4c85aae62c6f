import { schoolModel, userModel, classModel, studentModel, parentModel } from './src/models/index.js';
import { hashPassword } from './src/config/auth.js';

const createSampleData = async () => {
  try {
    console.log('🔄 Creating sample data in Firebase Firestore...');
    
    // Create sample school
    const school = await schoolModel.create({
      name: 'Nakipoğlu Cumhuriyet Anadolu Lisesi',
      address: 'Merkez <PERSON>esi, Eğitim Caddesi No:123, 34000 İstanbul',
      phone: '0212 555 0123',
      email: '<EMAIL>',
      principal_name: '<PERSON><PERSON><PERSON>'
    });
    
    console.log('✅ School created:', school.name);

    // Create admin user
    const adminPasswordHash = await hashPassword('admin123');
    const adminUser = await userModel.create({
      school_id: school.id,
      username: 'admin',
      email: '<EMAIL>',
      password_hash: adminPasswordHash,
      first_name: '<PERSON><PERSON>',
      last_name: '<PERSON><PERSON><PERSON>',
      role: 'admin',
      phone: '0532 123 4567',
      is_active: true
    });
    
    console.log('✅ Admin user created:', adminUser.username);

    // Create teacher user
    const teacherPasswordHash = await hashPassword('teacher123');
    const teacherUser = await userModel.create({
      school_id: school.id,
      username: 'teacher1',
      email: '<EMAIL>',
      password_hash: teacherPasswordHash,
      first_name: 'Ayşe',
      last_name: 'Yılmaz',
      role: 'teacher',
      phone: '0533 234 5678',
      is_active: true
    });
    
    console.log('✅ Teacher user created:', teacherUser.username);

    // Create sample classes
    const classes = [
      {
        school_id: school.id,
        name: '9A',
        grade_level: 9,
        section: 'A',
        teacher_id: teacherUser.id,
        academic_year: '2024-2025',
        is_active: true
      },
      {
        school_id: school.id,
        name: '9B',
        grade_level: 9,
        section: 'B',
        teacher_id: teacherUser.id,
        academic_year: '2024-2025',
        is_active: true
      },
      {
        school_id: school.id,
        name: '10A',
        grade_level: 10,
        section: 'A',
        teacher_id: teacherUser.id,
        academic_year: '2024-2025',
        is_active: true
      }
    ];

    const createdClasses = [];
    for (const classData of classes) {
      const createdClass = await classModel.create(classData);
      createdClasses.push(createdClass);
      console.log('✅ Class created:', createdClass.name);
    }

    // Create sample students and parents
    const studentsData = [
      {
        class_id: createdClasses[0].id, // 9A
        student_number: '2024001',
        first_name: 'Ahmet',
        last_name: 'Demir',
        date_of_birth: new Date('2008-03-15'),
        gender: 'male',
        address: 'Atatürk Mahallesi, 1. Sokak No:5, İstanbul',
        parent_phone: '0532 111 2233',
        parent_email: '<EMAIL>',
        emergency_contact: '0533 444 5566',
        parent_data: {
          first_name: 'Mehmet',
          last_name: 'Demir',
          relationship: 'father',
          occupation: 'Mühendis'
        }
      },
      {
        class_id: createdClasses[0].id, // 9A
        student_number: '2024002',
        first_name: 'Fatma',
        last_name: 'Kaya',
        date_of_birth: new Date('2008-07-22'),
        gender: 'female',
        address: 'Cumhuriyet Mahallesi, 2. Sokak No:12, İstanbul',
        parent_phone: '0533 222 3344',
        parent_email: '<EMAIL>',
        emergency_contact: '0534 555 6677',
        parent_data: {
          first_name: 'Ayşe',
          last_name: 'Kaya',
          relationship: 'mother',
          occupation: 'Öğretmen'
        }
      },
      {
        class_id: createdClasses[1].id, // 9B
        student_number: '2024003',
        first_name: 'Can',
        last_name: 'Özkan',
        date_of_birth: new Date('2008-11-10'),
        gender: 'male',
        address: 'Yeni Mahalle, 3. Sokak No:8, İstanbul',
        parent_phone: '0534 333 4455',
        parent_email: '<EMAIL>',
        emergency_contact: '0535 666 7788',
        parent_data: {
          first_name: 'Ali',
          last_name: 'Özkan',
          relationship: 'father',
          occupation: 'Doktor'
        }
      },
      {
        class_id: createdClasses[2].id, // 10A
        student_number: '2024004',
        first_name: 'Zeynep',
        last_name: 'Şahin',
        date_of_birth: new Date('2007-05-18'),
        gender: 'female',
        address: 'Merkez Mahallesi, 4. Sokak No:15, İstanbul',
        parent_phone: '0535 444 5566',
        parent_email: '<EMAIL>',
        emergency_contact: '0536 777 8899',
        parent_data: {
          first_name: 'Fatma',
          last_name: 'Şahin',
          relationship: 'mother',
          occupation: 'Hemşire'
        }
      }
    ];

    for (const studentData of studentsData) {
      const { parent_data, ...studentInfo } = studentData;
      
      // Create student
      const student = await studentModel.create({
        ...studentInfo,
        school_id: school.id,
        enrollment_date: new Date(),
        is_active: true
      });
      
      console.log('✅ Student created:', `${student.first_name} ${student.last_name}`);

      // Create parent
      const parent = await parentModel.create({
        student_id: student.id,
        first_name: parent_data.first_name,
        last_name: parent_data.last_name,
        phone: studentData.parent_phone,
        email: studentData.parent_email,
        address: studentData.address,
        relationship: parent_data.relationship,
        occupation: parent_data.occupation,
        is_primary_contact: true,
        is_active: true
      });
      
      console.log('✅ Parent created:', `${parent.first_name} ${parent.last_name}`);
    }

    console.log('\n🎉 Sample data creation completed successfully!');
    console.log('\n📋 Login credentials:');
    console.log('Admin: username=admin, password=admin123');
    console.log('Teacher: username=teacher1, password=teacher123');
    console.log('\n🏫 School:', school.name);
    console.log('👥 Classes created:', createdClasses.length);
    console.log('👨‍🎓 Students created:', studentsData.length);
    console.log('👨‍👩‍👧‍👦 Parents created:', studentsData.length);

  } catch (error) {
    console.error('❌ Failed to create sample data:', error);
    process.exit(1);
  }
};

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

// Start the data creation
createSampleData();
