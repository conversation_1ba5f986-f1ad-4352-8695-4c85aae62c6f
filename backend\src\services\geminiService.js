import { GoogleGenerativeAI } from '@google/generative-ai';
import { studentModel, classModel, parentModel, smsMessageModel } from '../models/index.js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Gemini AI client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-pro" });

// Available functions for the AI agent
const availableFunctions = {
  listStudents: async (params, userContext) => {
    const { className, gradeLevel, limit = 10 } = params;
    
    let whereConditions = {
      school_id: userContext.schoolId,
      is_active: true
    };

    if (gradeLevel) {
      // First get classes with the specified grade level
      const classes = await classModel.findAll({
        where: { 
          school_id: userContext.schoolId, 
          grade_level: parseInt(gradeLevel),
          is_active: true 
        }
      });
      
      if (classes.length > 0) {
        const classIds = classes.map(c => c.id);
        whereConditions.class_id = { op: 'in', value: classIds };
      }
    }

    if (className) {
      // Get classes with matching name
      const classes = await classModel.findAll({
        where: { 
          school_id: userContext.schoolId,
          name: { op: '>=', value: className }, // Firestore doesn't have LIKE, using >= for partial match
          is_active: true 
        }
      });
      
      if (classes.length > 0) {
        const classIds = classes.map(c => c.id);
        whereConditions.class_id = { op: 'in', value: classIds };
      }
    }

    const students = await studentModel.findAll({
      where: whereConditions,
      limit: parseInt(limit),
      orderBy: { field: 'first_name' }
    });

    // Get class information for each student
    const studentsWithClass = await Promise.all(
      students.map(async (student) => {
        const classInfo = await classModel.findById(student.class_id);
        return {
          id: student.id,
          name: `${student.first_name} ${student.last_name}`,
          studentNumber: student.student_number,
          class: classInfo?.name,
          gradeLevel: classInfo?.grade_level
        };
      })
    );

    return {
      success: true,
      data: studentsWithClass,
      count: studentsWithClass.length
    };
  },

  getStudentDetails: async (params, userContext) => {
    const { studentId, studentNumber } = params;
    
    let student;
    if (studentId) {
      student = await studentModel.findById(studentId);
    } else if (studentNumber) {
      student = await studentModel.findByStudentNumber(studentNumber);
    }

    if (!student || student.school_id !== userContext.schoolId) {
      return {
        success: false,
        error: 'Öğrenci bulunamadı'
      };
    }

    // Get class information
    const classInfo = await classModel.findById(student.class_id);
    
    // Get parent information
    const parents = await parentModel.findByStudent(student.id);

    return {
      success: true,
      data: {
        id: student.id,
        name: `${student.first_name} ${student.last_name}`,
        studentNumber: student.student_number,
        dateOfBirth: student.date_of_birth,
        gender: student.gender,
        address: student.address,
        class: {
          id: classInfo?.id,
          name: classInfo?.name,
          gradeLevel: classInfo?.grade_level,
          section: classInfo?.section
        },
        parents: parents.map(p => ({
          name: `${p.first_name} ${p.last_name}`,
          relationship: p.relationship,
          phone: p.phone,
          email: p.email,
          isPrimary: p.is_primary_contact
        })),
        enrollmentDate: student.enrollment_date,
        isActive: student.is_active
      }
    };
  },

  sendSms: async (params, userContext) => {
    const { recipientType, recipientId, message, isAiEnhanced = false, originalText } = params;
    
    try {
      // Create SMS message record
      const smsMessage = await smsMessageModel.create({
        school_id: userContext.schoolId,
        sender_id: userContext.userId,
        recipient_type: recipientType,
        recipient_id: recipientId,
        message_text: message,
        original_text: originalText,
        is_ai_enhanced: isAiEnhanced,
        status: 'pending'
      });

      return {
        success: true,
        data: {
          messageId: smsMessage.id,
          status: 'SMS gönderim kuyruğuna eklendi',
          recipientType,
          message
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `SMS gönderilemedi: ${error.message}`
      };
    }
  }
};

// Function definitions for Gemini function calling
const functionDefinitions = [
  {
    name: 'listStudents',
    description: 'Öğrenci listesini getir',
    parameters: {
      type: 'object',
      properties: {
        className: {
          type: 'string',
          description: 'Sınıf adı (opsiyonel)'
        },
        gradeLevel: {
          type: 'number',
          description: 'Sınıf seviyesi (opsiyonel)'
        },
        limit: {
          type: 'number',
          description: 'Maksimum öğrenci sayısı (varsayılan: 10)'
        }
      }
    }
  },
  {
    name: 'getStudentDetails',
    description: 'Öğrenci detaylarını getir',
    parameters: {
      type: 'object',
      properties: {
        studentId: {
          type: 'string',
          description: 'Öğrenci ID\'si'
        },
        studentNumber: {
          type: 'string',
          description: 'Öğrenci numarası'
        }
      }
    }
  },
  {
    name: 'sendSms',
    description: 'SMS gönder',
    parameters: {
      type: 'object',
      properties: {
        recipientType: {
          type: 'string',
          enum: ['individual', 'class', 'grade', 'all'],
          description: 'Alıcı tipi'
        },
        recipientId: {
          type: 'string',
          description: 'Alıcı ID\'si (individual için öğrenci ID, class için sınıf ID)'
        },
        message: {
          type: 'string',
          description: 'Gönderilecek mesaj'
        },
        isAiEnhanced: {
          type: 'boolean',
          description: 'AI ile geliştirilmiş mesaj mı?'
        },
        originalText: {
          type: 'string',
          description: 'Orijinal mesaj metni (AI geliştirilmişse)'
        }
      },
      required: ['recipientType', 'message']
    }
  }
];

// Enhance text with Gemini AI
export const enhanceTextWithGemini = async (text, tone = 'formal', context = '') => {
  try {
    const prompt = `
Aşağıdaki metni ${tone} bir tonda okul yönetimi için uygun hale getir. 
${context ? `Bağlam: ${context}` : ''}

Orijinal metin: "${text}"

Lütfen metni şu kurallara göre düzenle:
- ${tone === 'formal' ? 'Resmi ve saygılı' : tone === 'friendly' ? 'Samimi ve dostane' : tone === 'urgent' ? 'Acil ve net' : 'Bilgilendirici'} bir ton kullan
- Okul ortamına uygun dil kullan
- Türkçe dilbilgisi kurallarına uy
- Kısa ve anlaşılır ol
- Sadece düzenlenmiş metni döndür, açıklama yapma

Düzenlenmiş metin:`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const enhancedText = response.text().trim();

    return {
      success: true,
      enhancedText,
      originalText: text
    };
  } catch (error) {
    console.error('Gemini text enhancement error:', error);
    return {
      success: false,
      error: error.message,
      enhancedText: text // Fallback to original text
    };
  }
};

// Process AI chat message with Gemini
export const processGeminiMessage = async (message, userContext) => {
  try {
    const systemPrompt = `Sen akıllı okul yönetim sisteminin AI asistanısın. Türkçe konuşuyorsun ve okul yöneticilerine yardım ediyorsun.

Görevlerin:
1. Öğrenci listelerini göstermek
2. Öğrenci detaylarını getirmek  
3. SMS göndermek (bireysel veya toplu)

Kullanıcı: ${userContext.userName} (${userContext.role})
Okul: ${userContext.schoolName}

Kullanıcının isteklerini anlayıp uygun fonksiyonları çağır. Eğer belirsizlik varsa kullanıcıdan açıklama iste.

Kullanıcı mesajı: ${message}`;

    const result = await model.generateContent(systemPrompt);
    const response = await result.response;
    const responseText = response.text();

    // Simple function call detection (Gemini doesn't have built-in function calling like OpenAI)
    // We'll implement a basic pattern matching for function calls
    let functionCall = null;
    let functionResult = null;

    // Check for function call patterns
    if (responseText.includes('listStudents') || message.toLowerCase().includes('öğrenci listesi') || message.toLowerCase().includes('öğrencileri listele')) {
      const params = extractStudentListParams(message);
      functionResult = await availableFunctions.listStudents(params, userContext);
      functionCall = { name: 'listStudents', arguments: params, result: functionResult };
    } else if (responseText.includes('getStudentDetails') || message.toLowerCase().includes('öğrenci detay') || message.toLowerCase().includes('öğrenci bilgi')) {
      const params = extractStudentDetailsParams(message);
      if (params.studentId || params.studentNumber) {
        functionResult = await availableFunctions.getStudentDetails(params, userContext);
        functionCall = { name: 'getStudentDetails', arguments: params, result: functionResult };
      }
    } else if (responseText.includes('sendSms') || message.toLowerCase().includes('sms gönder') || message.toLowerCase().includes('mesaj gönder')) {
      const params = extractSmsParams(message);
      if (params.message) {
        functionResult = await availableFunctions.sendSms(params, userContext);
        functionCall = { name: 'sendSms', arguments: params, result: functionResult };
      }
    }

    // Generate final response based on function result
    let finalResponse = responseText;
    if (functionResult) {
      const followUpPrompt = `
Kullanıcı isteği: ${message}
Fonksiyon sonucu: ${JSON.stringify(functionResult)}

Bu sonuca göre kullanıcıya Türkçe, anlaşılır ve yardımcı bir yanıt ver. Sonuçları düzenli bir şekilde sun.`;

      const followUpResult = await model.generateContent(followUpPrompt);
      const followUpResponse = await followUpResult.response;
      finalResponse = followUpResponse.text();
    }

    return {
      success: true,
      response: finalResponse,
      functionCall
    };
  } catch (error) {
    console.error('Gemini chat processing error:', error);
    return {
      success: false,
      error: error.message,
      response: 'Üzgünüm, şu anda yardımcı olamıyorum. Lütfen daha sonra tekrar deneyin.'
    };
  }
};

// Helper functions to extract parameters from user messages
function extractStudentListParams(message) {
  const params = {};
  
  // Extract grade level
  const gradeMatch = message.match(/(\d+)\.?\s*sınıf/i);
  if (gradeMatch) {
    params.gradeLevel = parseInt(gradeMatch[1]);
  }
  
  // Extract class name
  const classMatch = message.match(/(\d+[A-Z]|\d+\/[A-Z])/i);
  if (classMatch) {
    params.className = classMatch[1];
  }
  
  // Extract limit
  const limitMatch = message.match(/(\d+)\s*(öğrenci|kişi)/i);
  if (limitMatch) {
    params.limit = parseInt(limitMatch[1]);
  }
  
  return params;
}

function extractStudentDetailsParams(message) {
  const params = {};
  
  // Extract student number
  const numberMatch = message.match(/(\d{3,})/);
  if (numberMatch) {
    params.studentNumber = numberMatch[1];
  }
  
  return params;
}

function extractSmsParams(message) {
  const params = {};
  
  // Simple message extraction - in a real implementation, this would be more sophisticated
  if (message.includes('sınıfa') || message.includes('sınıfına')) {
    params.recipientType = 'class';
  } else if (message.includes('herkese') || message.includes('tümüne')) {
    params.recipientType = 'all';
  } else {
    params.recipientType = 'individual';
  }
  
  // Extract message content (this is simplified)
  const messageMatch = message.match(/"([^"]+)"/);
  if (messageMatch) {
    params.message = messageMatch[1];
  }
  
  return params;
}
