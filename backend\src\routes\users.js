import express from 'express';
import { userModel } from '../models/index.js';

const router = express.Router();

// Get all users (with optional role filter)
export const getUsers = async (req, res) => {
  try {
    const schoolId = req.user.school_id;
    const { role, limit = 50 } = req.query;

    let whereConditions = {
      school_id: schoolId,
      is_active: true
    };

    if (role) {
      whereConditions.role = role;
    }

    const users = await userModel.findAll({
      where: whereConditions,
      limit: parseInt(limit),
      orderBy: { field: 'first_name' }
    });

    // Remove password_hash from response
    const safeUsers = users.map(user => {
      const { password_hash, ...safeUser } = user;
      return safeUser;
    });

    res.json({
      success: true,
      data: {
        users: safeUsers,
        total: safeUsers.length
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
};

// Get user by ID
export const getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    const schoolId = req.user.school_id;

    const user = await userModel.findById(id);
    
    if (!user || user.school_id !== schoolId) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Remove password_hash from response
    const { password_hash, ...safeUser } = user;

    res.json({
      success: true,
      data: safeUser
    });

  } catch (error) {
    console.error('Get user by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user details'
    });
  }
};

// User routes
router.get('/', getUsers);
router.get('/:id', getUserById);

export default router;
