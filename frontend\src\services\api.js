import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API functions
export const authAPI = {
  login: async (credentials) => {
    console.log('🔍 Login attempt:', credentials);
    console.log('🌐 API Base URL:', API_BASE_URL);
    try {
      const response = await api.post('/auth/login', credentials);
      console.log('✅ Login response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Login error:', error.response?.data || error.message);
      throw error;
    }
  },
  
  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },
  
  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data;
  },
  
  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  }
};

// Students API functions
export const studentsAPI = {
  getAll: async (params = {}) => {
    const response = await api.get('/students', { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/students/${id}`);
    return response.data;
  },

  search: async (query) => {
    const response = await api.get('/students/search', { params: { q: query } });
    return response.data;
  },

  create: async (studentData) => {
    const response = await api.post('/students', studentData);
    return response.data;
  },

  importCSV: async (file) => {
    const formData = new FormData();
    formData.append('csvFile', file);

    const response = await api.post('/students/import-csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  }
};

// Classes API functions
export const classesAPI = {
  getAll: async () => {
    const response = await api.get('/classes');
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/classes/${id}`);
    return response.data;
  },

  create: async (classData) => {
    const response = await api.post('/classes', classData);
    return response.data;
  },

  getStudents: async (classId) => {
    const response = await api.get(`/classes/${classId}/students`);
    return response.data;
  }
};

// Users API functions
export const usersAPI = {
  getAll: async (params = {}) => {
    const response = await api.get('/users', { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  }
};

// SMS API functions
export const smsAPI = {
  send: async (messageData) => {
    const response = await api.post('/sms/send', messageData);
    return response.data;
  },
  
  enhance: async (textData) => {
    const response = await api.post('/sms/enhance', textData);
    return response.data;
  },
  
  getHistory: async (params = {}) => {
    const response = await api.get('/sms/history', { params });
    return response.data;
  }
};

// AI Chat API functions
export const aiChatAPI = {
  sendMessage: async (sessionId, message) => {
    const response = await api.post('/ai/chat', { sessionId, message });
    return response.data;
  },
  
  getSessions: async () => {
    const response = await api.get('/ai/sessions');
    return response.data;
  },
  
  createSession: async (title) => {
    const response = await api.post('/ai/sessions', { title });
    return response.data;
  },
  
  getMessages: async (sessionId) => {
    const response = await api.get(`/ai/sessions/${sessionId}/messages`);
    return response.data;
  }
};

export default api;
