import express from 'express';
import {
  sendMessage,
  getChatSessions,
  createChatSession,
  getChatMessages,
  deleteChatSession,
  updateSessionTitle
} from '../controllers/aiController.js';
import { authenticate } from '../middleware/auth.js';

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Chat routes
router.post('/chat', sendMessage);
router.get('/sessions', getChatSessions);
router.post('/sessions', createChatSession);
router.get('/sessions/:sessionId/messages', getChatMessages);
router.put('/sessions/:sessionId/title', updateSessionTitle);
router.delete('/sessions/:sessionId', deleteChatSession);

export default router;
