import { studentModel, classModel, parentModel, schoolModel } from '../models/index.js';
import smsService from '../services/smsService.js';
import Joi from 'joi';
import multer from 'multer';
import csv from 'csv-parser';
import fs from 'fs';
import path from 'path';

// Validation schemas
const searchSchema = Joi.object({
  q: Joi.string().min(1).max(100).optional(),
  class_id: Joi.string().optional(),
  grade_level: Joi.number().integer().min(1).max(12).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20)
});

const createStudentSchema = Joi.object({
  class_id: Joi.string().required(),
  student_number: Joi.string().min(1).max(50).required(),
  first_name: Joi.string().min(1).max(100).required(),
  last_name: Joi.string().min(1).max(100).required(),
  date_of_birth: Joi.date().optional(),
  gender: Joi.string().valid('male', 'female').optional(),
  address: Joi.string().max(500).optional(),
  parent_first_name: Joi.string().min(1).max(100).required(),
  parent_last_name: Joi.string().min(1).max(100).required(),
  parent_phone: Joi.string().min(10).max(20).required(),
  parent_email: Joi.string().email().optional(),
  parent_address: Joi.string().max(500).optional(),
  parent_relationship: Joi.string().valid('mother', 'father', 'guardian').default('mother'),
  emergency_contact: Joi.string().min(10).max(20).optional(),
  medical_info: Joi.string().max(1000).optional()
});

// Search students
export const searchStudents = async (req, res) => {
  try {
    const { error, value } = searchSchema.validate(req.query);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { q, class_id, grade_level, page, limit } = value;
    const schoolId = req.user.school_id;

    // Simplified query - just get all students for the school
    const students = await studentModel.findAll({
      where: { school_id: schoolId },
      limit: limit
    });

    // Filter by search query, class, and grade level in memory
    let filteredStudents = students.filter(student => {
      // Filter by active status
      if (!student.is_active) return false;

      // Filter by search query
      if (q) {
        const searchTerm = q.toLowerCase();
        const matchesSearch = student.first_name.toLowerCase().includes(searchTerm) ||
                            student.last_name.toLowerCase().includes(searchTerm) ||
                            student.student_number.includes(searchTerm);
        if (!matchesSearch) return false;
      }

      // Filter by class_id
      if (class_id && student.class_id !== class_id) return false;

      return true;
    });

    // Get class information for each student
    const studentsWithClass = await Promise.all(
      filteredStudents.map(async (student) => {
        const classInfo = await classModel.findById(student.class_id);
        const parents = await parentModel.findByStudent(student.id);
        
        return {
          id: student.id,
          student_number: student.student_number,
          first_name: student.first_name,
          last_name: student.last_name,
          date_of_birth: student.date_of_birth,
          gender: student.gender,
          address: student.address,
          enrollment_date: student.enrollment_date,
          is_active: student.is_active,
          class: classInfo ? {
            id: classInfo.id,
            name: classInfo.name,
            grade_level: classInfo.grade_level,
            section: classInfo.section
          } : null,
          parents: parents.map(p => ({
            id: p.id,
            name: `${p.first_name} ${p.last_name}`,
            phone: p.phone,
            email: p.email,
            relationship: p.relationship,
            is_primary_contact: p.is_primary_contact
          }))
        };
      })
    );

    res.json({
      success: true,
      data: {
        students: studentsWithClass,
        pagination: {
          page,
          limit,
          total: filteredStudents.length
        }
      }
    });

  } catch (error) {
    console.error('Search students error:', error);
    res.status(500).json({
      success: false,
      message: 'Öğrenci arama sırasında hata oluştu',
      error: error.message
    });
  }
};

// Get student by ID
export const getStudentById = async (req, res) => {
  try {
    const { id } = req.params;
    const schoolId = req.user.school_id;

    const student = await studentModel.findById(id);
    if (!student || student.school_id !== schoolId) {
      return res.status(404).json({
        success: false,
        message: 'Öğrenci bulunamadı'
      });
    }

    // Get class information
    const classInfo = await classModel.findById(student.class_id);
    
    // Get parent information
    const parents = await parentModel.findByStudent(student.id);

    const studentData = {
      id: student.id,
      student_number: student.student_number,
      first_name: student.first_name,
      last_name: student.last_name,
      date_of_birth: student.date_of_birth,
      gender: student.gender,
      address: student.address,
      parent_phone: student.parent_phone,
      parent_email: student.parent_email,
      emergency_contact: student.emergency_contact,
      medical_info: student.medical_info,
      enrollment_date: student.enrollment_date,
      is_active: student.is_active,
      class: classInfo ? {
        id: classInfo.id,
        name: classInfo.name,
        grade_level: classInfo.grade_level,
        section: classInfo.section,
        academic_year: classInfo.academic_year
      } : null,
      parents: parents.map(p => ({
        id: p.id,
        first_name: p.first_name,
        last_name: p.last_name,
        phone: p.phone,
        email: p.email,
        address: p.address,
        relationship: p.relationship,
        occupation: p.occupation,
        is_primary_contact: p.is_primary_contact
      }))
    };

    res.json({
      success: true,
      data: studentData
    });

  } catch (error) {
    console.error('Get student error:', error);
    res.status(500).json({
      success: false,
      message: 'Öğrenci bilgileri alınırken hata oluştu',
      error: error.message
    });
  }
};

// Create new student
export const createStudent = async (req, res) => {
  try {
    const { error, value } = createStudentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const schoolId = req.user.school_id;
    const {
      class_id,
      student_number,
      first_name,
      last_name,
      date_of_birth,
      gender,
      address,
      parent_first_name,
      parent_last_name,
      parent_phone,
      parent_email,
      parent_address,
      parent_relationship,
      emergency_contact,
      medical_info
    } = value;

    // Check if student number already exists
    const existingStudent = await studentModel.findByStudentNumber(student_number);
    if (existingStudent) {
      return res.status(409).json({
        success: false,
        message: 'Bu öğrenci numarası zaten kullanılıyor'
      });
    }

    // Verify class exists and belongs to school
    const classInfo = await classModel.findById(class_id);
    if (!classInfo || classInfo.school_id !== schoolId) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz sınıf ID'
      });
    }

    // Create student
    const student = await studentModel.create({
      school_id: schoolId,
      class_id,
      student_number,
      first_name,
      last_name,
      date_of_birth,
      gender,
      address,
      parent_phone,
      parent_email,
      emergency_contact,
      medical_info
    });

    // Create parent
    const parent = await parentModel.create({
      student_id: student.id,
      first_name: parent_first_name,
      last_name: parent_last_name,
      phone: parent_phone,
      email: parent_email,
      address: parent_address || address,
      relationship: parent_relationship,
      is_primary_contact: true
    });

    res.status(201).json({
      success: true,
      data: {
        student: {
          id: student.id,
          student_number: student.student_number,
          first_name: student.first_name,
          last_name: student.last_name,
          class: {
            id: classInfo.id,
            name: classInfo.name,
            grade_level: classInfo.grade_level
          }
        },
        parent: {
          id: parent.id,
          name: `${parent.first_name} ${parent.last_name}`,
          phone: parent.phone,
          relationship: parent.relationship
        }
      },
      message: 'Öğrenci başarıyla oluşturuldu'
    });

  } catch (error) {
    console.error('Create student error:', error);
    res.status(500).json({
      success: false,
      message: 'Öğrenci oluşturulurken hata oluştu',
      error: error.message
    });
  }
};

// Update student
export const updateStudent = async (req, res) => {
  try {
    const { id } = req.params;
    const schoolId = req.user.school_id;

    const student = await studentModel.findById(id);
    if (!student || student.school_id !== schoolId) {
      return res.status(404).json({
        success: false,
        message: 'Öğrenci bulunamadı'
      });
    }

    // Validate update data (simplified validation)
    const allowedFields = [
      'first_name', 'last_name', 'date_of_birth', 'gender', 'address',
      'parent_phone', 'parent_email', 'emergency_contact', 'medical_info'
    ];

    const updateData = {};
    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key) && req.body[key] !== undefined) {
        updateData[key] = req.body[key];
      }
    });

    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Güncellenecek veri bulunamadı'
      });
    }

    const updatedStudent = await studentModel.update(id, updateData);

    res.json({
      success: true,
      data: updatedStudent,
      message: 'Öğrenci bilgileri başarıyla güncellendi'
    });

  } catch (error) {
    console.error('Update student error:', error);
    res.status(500).json({
      success: false,
      message: 'Öğrenci güncellenirken hata oluştu',
      error: error.message
    });
  }
};
