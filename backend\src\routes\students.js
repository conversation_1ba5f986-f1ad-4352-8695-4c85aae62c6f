import express from 'express';
import {
  searchStudents,
  getStudentById,
  createStudent,
  updateStudent
} from '../controllers/studentsController.js';
import { authenticate } from '../middleware/auth.js';

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Student routes
router.get('/', searchStudents); // Use search as default getAll
router.get('/search', searchStudents);
router.get('/:id', getStudentById);
router.post('/', createStudent);
router.put('/:id', updateStudent);

export default router;
