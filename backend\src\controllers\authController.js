import { userModel, schoolModel } from '../models/index.js';
import { generateToken, hashPassword, comparePassword } from '../config/auth.js';
import Joi from 'joi';

// Validation schemas
const loginSchema = Joi.object({
  username: Joi.string().required(),
  password: Joi.string().required()
});

const registerSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(30).required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  first_name: Joi.string().min(2).max(50).required(),
  last_name: Joi.string().min(2).max(50).required(),
  school_id: Joi.string().uuid().required(),
  role: Joi.string().valid('admin', 'teacher', 'principal').default('admin'),
  phone: Joi.string().optional()
});

// Login controller
export const login = async (req, res) => {
  try {
    // Validate input
    const { error, value } = loginSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { username, password } = value;

    // Find user by username
    const user = await userModel.findByUsername(username);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Verify password
    const isPasswordValid = await comparePassword(password, user.password_hash);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Get school information
    const school = await schoolModel.findById(user.school_id);

    // Update last login
    await userModel.update(user.id, { last_login: new Date() });

    // Generate token
    const token = generateToken({
      userId: user.id,
      username: user.username,
      role: user.role,
      schoolId: user.school_id
    });

    // Return user data (excluding password)
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      phone: user.phone,
      school: school ? {
        id: school.id,
        name: school.name
      } : null
    };

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userData,
        token
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed'
    });
  }
};

// Register controller (for creating new admin users)
export const register = async (req, res) => {
  try {
    // Validate input
    const { error, value } = registerSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { username, email, password, first_name, last_name, school_id, role, phone } = value;

    // Check if username or email already exists
    const existingUserByUsername = await userModel.findByUsername(username);
    const existingUserByEmail = await userModel.findByEmail(email);

    if (existingUserByUsername || existingUserByEmail) {
      return res.status(409).json({
        success: false,
        message: 'Username or email already exists'
      });
    }

    // Verify school exists
    const school = await schoolModel.findById(school_id);
    if (!school) {
      return res.status(400).json({
        success: false,
        message: 'Invalid school ID'
      });
    }

    // Hash password
    const password_hash = await hashPassword(password);

    // Create user
    const user = await userModel.create({
      username,
      email,
      password_hash,
      first_name,
      last_name,
      school_id,
      role,
      phone
    });

    // Return user data (excluding password)
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      phone: user.phone,
      school_id: user.school_id
    };

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: { user: userData }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed'
    });
  }
};

// Get current user profile
export const getProfile = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password_hash'] },
      include: [{
        model: School,
        attributes: ['id', 'name', 'address', 'phone', 'email']
      }]
    });

    res.json({
      success: true,
      data: { user }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user profile'
    });
  }
};

// Logout (client-side token removal, but we can log it)
export const logout = async (req, res) => {
  try {
    // In a more sophisticated setup, you might want to blacklist the token
    // For now, we'll just return success as the client will remove the token
    
    res.json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed'
    });
  }
};
