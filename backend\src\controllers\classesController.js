import { classModel, studentModel, userModel, schoolModel } from '../models/index.js';

// Get all classes for the user's school
export const getClasses = async (req, res) => {
  try {
    const schoolId = req.user.school_id;
    
    // Get all classes for the school (simplified query)
    const allClasses = await classModel.findAll({
      where: { school_id: schoolId }
    });

    // Filter active classes in memory
    const classes = allClasses.filter(cls => cls.is_active);

    // Add teacher and student count information to each class
    const classesWithDetails = await Promise.all(
      classes.map(async (cls) => {
        // Get teacher information
        let teacher = null;
        if (cls.teacher_id) {
          teacher = await userModel.findById(cls.teacher_id);
        }

        // Get student count
        const students = await studentModel.findByClass(cls.id);
        
        return {
          id: cls.id,
          name: cls.name,
          grade_level: cls.grade_level,
          section: cls.section,
          academic_year: cls.academic_year,
          is_active: cls.is_active,
          teacher: teacher ? {
            id: teacher.id,
            first_name: teacher.first_name,
            last_name: teacher.last_name,
            email: teacher.email
          } : null,
          student_count: students.length,
          created_at: cls.created_at,
          updated_at: cls.updated_at
        };
      })
    );

    res.json({
      success: true,
      data: { classes: classesWithDetails }
    });

  } catch (error) {
    console.error('Get classes error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch classes'
    });
  }
};

// Get class by ID with students
export const getClassById = async (req, res) => {
  try {
    const { id } = req.params;
    const schoolId = req.user.school_id;

    const classData = await classModel.findById(id);
    
    if (!classData || classData.school_id !== schoolId) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    // Get teacher information
    let teacher = null;
    if (classData.teacher_id) {
      teacher = await userModel.findById(classData.teacher_id);
    }

    // Get students in the class
    const students = await studentModel.findByClass(id);

    const response = {
      id: classData.id,
      name: classData.name,
      grade_level: classData.grade_level,
      section: classData.section,
      academic_year: classData.academic_year,
      is_active: classData.is_active,
      teacher: teacher ? {
        id: teacher.id,
        first_name: teacher.first_name,
        last_name: teacher.last_name,
        email: teacher.email,
        phone: teacher.phone
      } : null,
      students: students.map(student => ({
        id: student.id,
        first_name: student.first_name,
        last_name: student.last_name,
        student_number: student.student_number
      })),
      student_count: students.length,
      created_at: classData.created_at,
      updated_at: classData.updated_at
    };

    res.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('Get class by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch class details'
    });
  }
};

// Create new class
export const createClass = async (req, res) => {
  try {
    const schoolId = req.user.school_id;
    const { name, grade_level, section, teacher_id, academic_year } = req.body;

    // Validate required fields
    if (!name || !grade_level || !section || !academic_year) {
      return res.status(400).json({
        success: false,
        message: 'Name, grade level, section, and academic year are required'
      });
    }

    // Check if class with same name already exists
    const existingClasses = await classModel.findAll({
      where: {
        school_id: schoolId,
        name: name,
        is_active: true
      }
    });

    if (existingClasses.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'A class with this name already exists'
      });
    }

    // Verify teacher exists if provided
    if (teacher_id) {
      const teacher = await userModel.findById(teacher_id);
      if (!teacher || teacher.school_id !== schoolId || teacher.role !== 'teacher') {
        return res.status(400).json({
          success: false,
          message: 'Invalid teacher ID'
        });
      }
    }

    // Create class
    const newClass = await classModel.create({
      school_id: schoolId,
      name,
      grade_level: parseInt(grade_level),
      section,
      teacher_id: teacher_id || null,
      academic_year,
      is_active: true
    });

    // Get teacher info if assigned
    let teacher = null;
    if (newClass.teacher_id) {
      teacher = await userModel.findById(newClass.teacher_id);
    }

    const response = {
      id: newClass.id,
      name: newClass.name,
      grade_level: newClass.grade_level,
      section: newClass.section,
      academic_year: newClass.academic_year,
      is_active: newClass.is_active,
      teacher: teacher ? {
        id: teacher.id,
        first_name: teacher.first_name,
        last_name: teacher.last_name,
        email: teacher.email
      } : null,
      student_count: 0,
      created_at: newClass.created_at,
      updated_at: newClass.updated_at
    };

    res.status(201).json({
      success: true,
      data: response,
      message: 'Class created successfully'
    });

  } catch (error) {
    console.error('Create class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create class'
    });
  }
};
