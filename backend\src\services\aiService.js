import { GoogleGenerativeAI } from '@google/generative-ai';
import { studentModel, classModel, parentModel, smsMessageModel } from '../models/firestore.js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Gemini AI client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-pro" });

// Available functions for the AI agent
const availableFunctions = {
  listStudents: async (params, userContext) => {
    const { className, gradeLevel, limit = 10 } = params;
    
    const whereConditions = {
      school_id: userContext.schoolId,
      is_active: true
    };

    const includeConditions = [
      {
        model: Class,
        attributes: ['id', 'name', 'grade_level', 'section'],
        where: {}
      }
    ];

    if (className) {
      includeConditions[0].where.name = { [Op.iLike]: `%${className}%` };
    }
    if (gradeLevel) {
      includeConditions[0].where.grade_level = gradeLevel;
    }

    const students = await Student.findAll({
      where: whereConditions,
      include: includeConditions,
      limit: parseInt(limit),
      order: [['first_name', 'ASC']]
    });

    return {
      success: true,
      data: students.map(s => ({
        id: s.id,
        name: `${s.first_name} ${s.last_name}`,
        studentNumber: s.student_number,
        class: s.Class?.name,
        gradeLevel: s.Class?.grade_level
      })),
      message: `${students.length} öğrenci listelendi`
    };
  },

  getStudentDetails: async (params, userContext) => {
    const { studentId, studentNumber } = params;
    
    const whereConditions = {
      school_id: userContext.schoolId,
      is_active: true
    };

    if (studentId) {
      whereConditions.id = studentId;
    } else if (studentNumber) {
      whereConditions.student_number = studentNumber;
    } else {
      return { success: false, message: 'Öğrenci ID veya numarası gerekli' };
    }

    const student = await Student.findOne({
      where: whereConditions,
      include: [
        {
          model: Class,
          attributes: ['id', 'name', 'grade_level', 'section']
        },
        {
          model: Parent,
          attributes: ['id', 'first_name', 'last_name', 'phone', 'relationship', 'is_primary_contact']
        }
      ]
    });

    if (!student) {
      return { success: false, message: 'Öğrenci bulunamadı' };
    }

    return {
      success: true,
      data: {
        id: student.id,
        name: `${student.first_name} ${student.last_name}`,
        studentNumber: student.student_number,
        class: student.Class?.name,
        gradeLevel: student.Class?.grade_level,
        parents: student.Parents?.map(p => ({
          name: `${p.first_name} ${p.last_name}`,
          phone: p.phone,
          relationship: p.relationship,
          isPrimary: p.is_primary_contact
        }))
      },
      message: 'Öğrenci detayları getirildi'
    };
  },

  sendSmsToStudent: async (params, userContext) => {
    const { studentId, studentNumber, message } = params;
    
    if (!message) {
      return { success: false, message: 'Mesaj içeriği gerekli' };
    }

    // Find student
    const whereConditions = {
      school_id: userContext.schoolId,
      is_active: true
    };

    if (studentId) {
      whereConditions.id = studentId;
    } else if (studentNumber) {
      whereConditions.student_number = studentNumber;
    } else {
      return { success: false, message: 'Öğrenci ID veya numarası gerekli' };
    }

    const student = await Student.findOne({
      where: whereConditions,
      include: [
        {
          model: Parent,
          where: { is_primary_contact: true },
          required: false
        }
      ]
    });

    if (!student) {
      return { success: false, message: 'Öğrenci bulunamadı' };
    }

    if (!student.Parents || student.Parents.length === 0) {
      return { success: false, message: 'Öğrencinin veli bilgisi bulunamadı' };
    }

    // Create SMS message record
    const smsMessage = await SmsMessage.create({
      school_id: userContext.schoolId,
      sender_id: userContext.userId,
      recipient_type: 'individual',
      recipient_id: student.id,
      message_text: message,
      status: 'pending'
    });

    return {
      success: true,
      data: {
        messageId: smsMessage.id,
        studentName: `${student.first_name} ${student.last_name}`,
        parentPhone: student.Parents[0].phone,
        message: message
      },
      message: 'SMS gönderim talebi oluşturuldu'
    };
  },

  sendSmsToClass: async (params, userContext) => {
    const { className, gradeLevel, message } = params;
    
    if (!message) {
      return { success: false, message: 'Mesaj içeriği gerekli' };
    }

    // Find class
    const classWhereConditions = {
      school_id: userContext.schoolId,
      is_active: true
    };

    if (className) {
      classWhereConditions.name = { [Op.iLike]: `%${className}%` };
    }
    if (gradeLevel) {
      classWhereConditions.grade_level = gradeLevel;
    }

    const targetClass = await Class.findOne({
      where: classWhereConditions
    });

    if (!targetClass) {
      return { success: false, message: 'Sınıf bulunamadı' };
    }

    // Get students in class
    const students = await Student.findAll({
      where: {
        class_id: targetClass.id,
        is_active: true
      },
      include: [
        {
          model: Parent,
          where: { is_primary_contact: true },
          required: false
        }
      ]
    });

    if (students.length === 0) {
      return { success: false, message: 'Sınıfta öğrenci bulunamadı' };
    }

    // Create SMS message record
    const smsMessage = await SmsMessage.create({
      school_id: userContext.schoolId,
      sender_id: userContext.userId,
      recipient_type: 'class',
      recipient_id: targetClass.id,
      message_text: message,
      status: 'pending'
    });

    const studentsWithParents = students.filter(s => s.Parents && s.Parents.length > 0);

    return {
      success: true,
      data: {
        messageId: smsMessage.id,
        className: targetClass.name,
        studentCount: students.length,
        parentCount: studentsWithParents.length,
        message: message
      },
      message: `${targetClass.name} sınıfındaki ${studentsWithParents.length} veliye SMS gönderim talebi oluşturuldu`
    };
  },

  getAbsentStudents: async (params, userContext) => {
    const { date = new Date().toISOString().split('T')[0] } = params;
    
    // This would typically check an attendance table
    // For now, we'll return a placeholder response
    return {
      success: true,
      data: [],
      message: `${date} tarihinde devamsızlık kaydı bulunamadı (Devamsızlık sistemi henüz aktif değil)`
    };
  }
};

// Function definitions for OpenAI
const functionDefinitions = [
  {
    name: 'listStudents',
    description: 'List students with optional filtering by class name or grade level',
    parameters: {
      type: 'object',
      properties: {
        className: {
          type: 'string',
          description: 'Class name to filter (e.g., "9-A", "10-B")'
        },
        gradeLevel: {
          type: 'integer',
          description: 'Grade level to filter (9, 10, 11, 12)'
        },
        limit: {
          type: 'integer',
          description: 'Maximum number of students to return',
          default: 10
        }
      }
    }
  },
  {
    name: 'getStudentDetails',
    description: 'Get detailed information about a specific student',
    parameters: {
      type: 'object',
      properties: {
        studentId: {
          type: 'string',
          description: 'Student UUID'
        },
        studentNumber: {
          type: 'string',
          description: 'Student number'
        }
      },
      oneOf: [
        { required: ['studentId'] },
        { required: ['studentNumber'] }
      ]
    }
  },
  {
    name: 'sendSmsToStudent',
    description: 'Send SMS to a specific student\'s parent',
    parameters: {
      type: 'object',
      properties: {
        studentId: {
          type: 'string',
          description: 'Student UUID'
        },
        studentNumber: {
          type: 'string',
          description: 'Student number'
        },
        message: {
          type: 'string',
          description: 'SMS message content'
        }
      },
      required: ['message'],
      oneOf: [
        { required: ['studentId', 'message'] },
        { required: ['studentNumber', 'message'] }
      ]
    }
  },
  {
    name: 'sendSmsToClass',
    description: 'Send SMS to all parents in a specific class',
    parameters: {
      type: 'object',
      properties: {
        className: {
          type: 'string',
          description: 'Class name (e.g., "9-A", "10-B")'
        },
        gradeLevel: {
          type: 'integer',
          description: 'Grade level (9, 10, 11, 12)'
        },
        message: {
          type: 'string',
          description: 'SMS message content'
        }
      },
      required: ['message']
    }
  },
  {
    name: 'getAbsentStudents',
    description: 'Get list of absent students for a specific date',
    parameters: {
      type: 'object',
      properties: {
        date: {
          type: 'string',
          description: 'Date in YYYY-MM-DD format',
          default: 'today'
        }
      }
    }
  }
];

// Process AI chat message
export const processAiMessage = async (message, userContext) => {
  try {
    const systemPrompt = `Sen akıllı okul yönetim sisteminin AI asistanısın. Türkçe konuşuyorsun ve okul yöneticilerine yardım ediyorsun.

Görevlerin:
1. Öğrenci listelerini göstermek
2. Öğrenci detaylarını getirmek  
3. SMS göndermek (bireysel veya toplu)
4. Devamsızlık kayıtlarını kontrol etmek

Kullanıcı: ${userContext.userName} (${userContext.role})
Okul: ${userContext.schoolName}

Kullanıcının isteklerini anlayıp uygun fonksiyonları çağır. Eğer belirsizlik varsa kullanıcıdan açıklama iste.`;

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: message }
      ],
      functions: functionDefinitions,
      function_call: 'auto',
      temperature: 0.7
    });

    const responseMessage = completion.choices[0].message;

    // If AI wants to call a function
    if (responseMessage.function_call) {
      const functionName = responseMessage.function_call.name;
      const functionArgs = JSON.parse(responseMessage.function_call.arguments);

      if (availableFunctions[functionName]) {
        const functionResult = await availableFunctions[functionName](functionArgs, userContext);
        
        // Get AI's response to the function result
        const followUpCompletion = await openai.chat.completions.create({
          model: 'gpt-4',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: message },
            responseMessage,
            {
              role: 'function',
              name: functionName,
              content: JSON.stringify(functionResult)
            }
          ],
          temperature: 0.7
        });

        return {
          success: true,
          response: followUpCompletion.choices[0].message.content,
          functionCall: {
            name: functionName,
            arguments: functionArgs,
            result: functionResult
          }
        };
      } else {
        return {
          success: false,
          response: 'Üzgünüm, bu fonksiyon şu anda kullanılamıyor.',
          error: 'Function not available'
        };
      }
    }

    // Regular response without function call
    return {
      success: true,
      response: responseMessage.content
    };

  } catch (error) {
    console.error('AI processing error:', error);
    return {
      success: false,
      response: 'Üzgünüm, şu anda bir teknik sorun yaşıyorum. Lütfen daha sonra tekrar deneyin.',
      error: error.message
    };
  }
};
